<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <el-table-column label="行号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.num`" :rules="formRules.num" class="mb-0px!">
            <el-input v-model="row.num" placeholder="请输入行号" />
          </el-form-item>
        </template>
      </el-table-column>
       <el-table-column label="发货通知单单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.noticeNo`" :rules="formRules.noticeNo" class="mb-0px!">
            <el-input v-model="row.noticeNo" placeholder="请输入发货通知单单号" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="销售单ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.saleOrderId`" :rules="formRules.saleOrderId" class="mb-0px!">
            <el-input v-model="row.saleOrderId" placeholder="请输入销售单ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="销售单单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.saleOrderNo`" :rules="formRules.saleOrderNo" class="mb-0px!">
            <el-input v-model="row.saleOrderNo" placeholder="请输入销售单单号" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialId`" :rules="formRules.materialId" class="mb-0px!">
            <el-input v-model="row.materialId" placeholder="请输入物料ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="物料编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="请输入物料编号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialName`" :rules="formRules.materialName" class="mb-0px!">
            <el-input v-model="row.materialName" placeholder="请输入物料名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料规格" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialSpec`" :rules="formRules.materialSpec" class="mb-0px!">
            <el-input v-model="row.materialSpec" placeholder="请输入物料规格" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料发货数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialQuantity`" :rules="formRules.materialQuantity" class="mb-0px!">
            <el-input v-model="row.materialQuantity" placeholder="请输入物料发货数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料单价" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialUnitPrice`" :rules="formRules.materialUnitPrice" class="mb-0px!">
            <el-input v-model="row.materialUnitPrice" placeholder="请输入物料单价" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料总金额" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.totalAmount`" :rules="formRules.totalAmount" class="mb-0px!">
            <el-input v-model="row.totalAmount" placeholder="请输入物料总金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.baseQuantity`" :rules="formRules.baseQuantity" class="mb-0px!">
            <el-input v-model="row.baseQuantity" placeholder="请输入基本单位数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="要求" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.packagingRequirements`" :rules="formRules.packagingRequirements" class="mb-0px!">
            <el-input v-model="row.packagingRequirements" placeholder="请输入要求" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="源单类型" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceType`" :rules="formRules.sourceType" class="mb-0px!">
            <el-select v-model="row.sourceType" placeholder="请选择源单类型">
                <el-option label="请选择字典生成" value="" />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="源单编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceId`" :rules="formRules.sourceId" class="mb-0px!">
            <el-input v-model="row.sourceId" placeholder="请输入源单编号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="源单单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceNo`" :rules="formRules.sourceNo" class="mb-0px!">
            <el-input v-model="row.sourceNo" placeholder="请输入源单单号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="仓库编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" :rules="formRules.warehouseId" class="mb-0px!">
            <el-input v-model="row.warehouseId" placeholder="请输入仓库编号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="交货日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.deliveryDate`" :rules="formRules.deliveryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.deliveryDate"
              type="date"
              value-format="x"
              placeholder="选择交货日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="金蝶系统订单ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.kdOrderId`" :rules="formRules.kdOrderId" class="mb-0px!">
            <el-input v-model="row.kdOrderId" placeholder="请输入金蝶系统订单ID" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="金蝶系统ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.kdId`" :rules="formRules.kdId" class="mb-0px!">
            <el-input v-model="row.kdId" placeholder="请输入金蝶系统ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="创建者名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.creatorName`" :rules="formRules.creatorName" class="mb-0px!">
            <el-input v-model="row.creatorName" placeholder="请输入创建者名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="更新者名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.updaterName`" :rules="formRules.updaterName" class="mb-0px!">
            <el-input v-model="row.updaterName" placeholder="请输入更新者名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="租户名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.tenantName`" :rules="formRules.tenantName" class="mb-0px!">
            <el-input v-model="row.tenantName" placeholder="请输入租户名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加发货通知单明细</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { DeliveryNoticeApi } from '@/api/scm/sale/deliverynotice'

const props = defineProps<{
  noticeId: undefined // 发货通知单ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref([])
const formRules = reactive({
  noticeId: [{ required: true, message: '发货通知单ID不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.noticeId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await DeliveryNoticeApi.getDeliveryNoticeDetailListByNoticeId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    num: undefined,
    noticeId: undefined,
    noticeNo: undefined,
    saleOrderId: undefined,
    saleOrderNo: undefined,
    materialId: undefined,
    materialCode: undefined,
    materialName: undefined,
    materialSpec: undefined,
    materialQuantity: undefined,
    materialUnitPrice: undefined,
    totalAmount: undefined,
    baseQuantity: undefined,
    packagingRequirements: undefined,
    sourceType: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    warehouseId: undefined,
    deliveryDate: undefined,
    kdOrderId: undefined,
    kdId: undefined,
    remark: undefined,
    creatorName: undefined,
    updaterName: undefined,
    tenantName: undefined,
  }
  row.noticeId = props.noticeId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>
