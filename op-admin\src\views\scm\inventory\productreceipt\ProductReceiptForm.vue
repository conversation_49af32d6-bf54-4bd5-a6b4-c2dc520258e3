<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单号" prop="orderNo">
            <el-input v-model="formData.orderNo" placeholder="请输入单号(自动生成)" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务类型" prop="bizType">
            <el-select v-model="formData.bizType" placeholder="请选择业务类型">
              <el-option label="请选择字典生成" value="" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源类型" prop="sourceType">
            <el-select v-model="formData.sourceType" placeholder="请选择来源类型">
              <el-option 
              v-for="item in material_source" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="来源单ID" prop="sourceId">
            <el-input v-model="formData.sourceId" placeholder="请输入来源单ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="来源单编号" prop="sourceNo">
            <el-input v-model="formData.sourceNo" placeholder="请输入来源单编号" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="交易对象ID" prop="objectId">
            <el-input v-model="formData.objectId" placeholder="请输入交易对象ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="交易对象名称" prop="objectName">
            <el-input v-model="formData.objectName" placeholder="请输入交易对象名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="交易对象订单号" prop="objectOrderNo">
            <el-input v-model="formData.objectOrderNo" placeholder="请输入交易对象订单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="交易日期" prop="date">
            <el-date-picker
              v-model="formData.date"
              type="date"
              value-format="x"
              placeholder="选择交易日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库名称" prop="warehouseId">
            <el-tree-select v-model="formData.warehouseId" :data="warehouseList" placeholder="请选择仓库"/>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="科目ID" prop="accountId">
            <el-input v-model="formData.accountId" placeholder="请输入科目ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="摘要" prop="note">
            <el-input v-model="formData.note" placeholder="请输入摘要" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="审批状态" prop="approveStatus">
            <el-radio-group v-model="formData.approveStatus">
              <el-radio 
              v-for="item in approve_status" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批单号" prop="approveNo">
            <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人ID" prop="approverId">
            <el-input v-model="formData.approverId" placeholder="请输入审批人ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人" prop="approverName">
            <el-input v-model="formData.approverName" placeholder="请输入审批人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批时间" prop="approveDate">
            <el-date-picker
              v-model="formData.approveDate"
              type="date"
              value-format="x"
              placeholder="选择审批时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门ID" prop="deptId">
            <el-input v-model="formData.deptId" placeholder="请输入部门ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务员ID" prop="empId">
            <el-input v-model="formData.empId" placeholder="请输入业务员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员ID" prop="managerId">
            <el-input v-model="formData.managerId" placeholder="请输入管理员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员1ID" prop="manger1Id">
            <el-input v-model="formData.manger1Id" placeholder="请输入管理员1ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="记账ID" prop="accountantId">
            <el-input v-model="formData.accountantId" placeholder="请输入记账ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检验员ID" prop="checkerId">
            <el-input v-model="formData.checkerId" placeholder="请输入检验员ID" />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="产品入库明细" name="productReceiptDetail">
        <ProductReceiptDetailForm ref="productReceiptDetailFormRef" :biz-order-id="formData.id" :warehouse-id="formData.warehouseId"/>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ProductReceiptApi, ProductReceiptVO } from '@/api/scm/inventory/productreceipt'
import ProductReceiptDetailForm from './components/ProductReceiptDetailForm.vue'
import { DICT_TYPE,getStrDictOptions,getDictLabel } from '@/utils/dict'
import { WarehouseApi,WarehouseVO } from '@/api/scm/inventory/warehouse'
import { handleTree } from '@/utils/tree'
/** 产品入库 表单 */
defineOptions({ name: 'ProductReceiptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: undefined,
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  approveStatus: undefined,
  approveNo: undefined,
  approverId: undefined,
  approverName: undefined,
  approveDate: undefined,
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const warehouseList = ref<any[]>([])
/** 子表的表单 */
const subTabsName = ref('productReceiptDetail')
const productReceiptDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  await initWarehouseList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProductReceiptApi.getProductReceipt(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await productReceiptDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'productReceiptDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProductReceiptVO
    // 拼接子表的数据
    data.productReceiptDetails = productReceiptDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await ProductReceiptApi.createProductReceipt(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProductReceiptApi.updateProductReceipt(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const initWarehouseList = async () => {
  const res = await WarehouseApi.getWarehouseList({
    pageNo:1,
    pageSize:100
  })
  const formatTreeData = (list) => {
    return list.map(item => {
      const node:any = {
        id: item.id,
        label: item.name,
        value: item.id,
        parentId: item.parentId
      }
      
      if (item.children && item.children.length > 0) {
        node.children = formatTreeData(item.children)
      }
      return node
    })
  }
  const warehouseTree = handleTree(res, 'id', 'parentId')
  warehouseList.value = formatTreeData(warehouseTree)
  console.log(warehouseList.value)
  warehouseList.value = warehouseList.value.filter(item => item.label === '产成品仓' || item.label === '半成品仓')
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    orderNo: undefined,
    bizType: undefined,
    sourceType: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    objectId: undefined,
    objectName: undefined,
    objectOrderNo: undefined,
    date: undefined,
    warehouseId: undefined,
    accountId: undefined,
    note: undefined,
    remark: undefined,
    approveStatus: undefined,
    approveNo: undefined,
    approverId: undefined,
    approverName: undefined,
    approveDate: undefined,
    deptId: undefined,
    empId: undefined,
    managerId: undefined,
    manger1Id: undefined,
    accountantId: undefined,
    checkerId: undefined,
  }
  formRef.value?.resetFields()
}
</script>
