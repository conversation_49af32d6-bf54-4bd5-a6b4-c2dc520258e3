<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <el-form-item label="单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="bizType">
        <el-select
          v-model="queryParams.bizType"
          placeholder="请选择业务类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="来源类型" prop="sourceType">
        <el-select
          v-model="queryParams.sourceType"
          placeholder="请选择来源类型"
          clearable
          class="!w-240px"
        >
          <el-option 
          v-for="item in material_source" 
          :key="item.value" 
          :label="item.label" 
          :value="item.value" 
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="来源单ID" prop="sourceId">
        <el-input
          v-model="queryParams.sourceId"
          placeholder="请输入来源单ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="来源单编号" prop="sourceNo">
        <el-input
          v-model="queryParams.sourceNo"
          placeholder="请输入来源单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="交易对象ID" prop="objectId">
        <el-input
          v-model="queryParams.objectId"
          placeholder="请输入交易对象ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="交易对象名称" prop="objectName">
        <el-input
          v-model="queryParams.objectName"
          placeholder="请输入交易对象名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="交易对象订单号" prop="objectOrderNo">
        <el-input
          v-model="queryParams.objectOrderNo"
          placeholder="请输入交易对象订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="交易日期" prop="date">
        <el-date-picker
          v-model="queryParams.date"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="仓库ID" prop="warehouseId">
        <el-input
          v-model="queryParams.warehouseId"
          placeholder="请输入仓库ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="科目ID" prop="accountId">
        <el-input
          v-model="queryParams.accountId"
          placeholder="请输入科目ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="摘要" prop="note">
        <el-input
          v-model="queryParams.note"
          placeholder="请输入摘要"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审批状态" prop="approveStatus">
        <el-select
          v-model="queryParams.approveStatus"
          placeholder="请选择审批状态"
          clearable
          class="!w-240px"
        >
          <el-option 
          v-for="item in approve_status" 
          :key="item.value" 
          :label="item.label" 
          :value="item.value" 
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批单号" prop="approveNo">
        <el-input
          v-model="queryParams.approveNo"
          placeholder="请输入审批单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="审批人ID" prop="approverId">
        <el-input
          v-model="queryParams.approverId"
          placeholder="请输入审批人ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="审批人" prop="approverName">
        <el-input
          v-model="queryParams.approverName"
          placeholder="请输入审批人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审批时间" prop="approveDate">
        <el-date-picker
          v-model="queryParams.approveDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="部门ID" prop="deptId">
        <el-input
          v-model="queryParams.deptId"
          placeholder="请输入部门ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="业务员ID" prop="empId">
        <el-input
          v-model="queryParams.empId"
          placeholder="请输入业务员ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="管理员ID" prop="managerId">
        <el-input
          v-model="queryParams.managerId"
          placeholder="请输入管理员ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="管理员1ID" prop="manger1Id">
        <el-input
          v-model="queryParams.manger1Id"
          placeholder="请输入管理员1ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="记账ID" prop="accountantId">
        <el-input
          v-model="queryParams.accountantId"
          placeholder="请输入记账ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="检验员ID" prop="checkerId">
        <el-input
          v-model="queryParams.checkerId"
          placeholder="请输入检验员ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['inventory:delivery-receipt:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['inventory:delivery-receipt:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
      :span-method="objectSpanMethod"
      height="600"
      style="width: 100%"
    >
      <!-- 最重要的主单据信息 -->
      <el-table-column label="单号" align="left" prop="orderNo" width="120px" fixed="left"/>
      <el-table-column label="业务类型" align="left" prop="bizType" width="100px" fixed="left"/>

      <!-- 核心明细信息 - 最重要的业务数据 -->
      <el-table-column label="序号" align="center" prop="detail.num" width="60px" />
      <el-table-column label="物料名称" align="left" prop="detail.materialName" width="180px" fixed="left"/>
      <el-table-column label="物料编号" align="left" prop="detail.materialCode" width="120px" />
      <el-table-column label="实发数量" align="right" prop="detail.fulfilledQuantity" width="100px"/>
      <el-table-column label="应发数量" align="right" prop="detail.plannedQuantity" width="100px"/>
      <el-table-column label="单位" align="center" prop="detail.unit" width="80px" />
      <el-table-column label="单价" align="right" prop="detail.unitPrice" width="100px"/>
      <el-table-column label="金额" align="right" prop="detail.amount" width="100px"/>
      <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>

      <!-- 重要的主单据业务信息 -->
      <el-table-column label="来源类型" align="center" prop="sourceType" width="100px">
        <template #default="scope">
          {{ getDictLabel('MATERIAL_SOURCE', scope.row.sourceType) }}
        </template>
      </el-table-column>
      <el-table-column label="来源单编号" align="left" prop="sourceNo" width="120px"/>
      <el-table-column label="交易对象名称" align="left" prop="objectName" width="150px"/>
      <el-table-column
        label="交易日期"
        align="center"
        prop="date"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column label="审批状态" align="center" prop="approveStatus" width="100px">
        <template #default="scope">
          {{ getDictLabel('APPROVE_STATUS', scope.row.approveStatus) }}
        </template>
      </el-table-column>

      <!-- 税务和财务信息 -->
      <el-table-column label="含税单价" align="right" prop="detail.taxPrice" width="100px"/>
      <el-table-column label="含税金额" align="right" prop="detail.taxAmount" width="110px"/>

      <!-- 基本单位信息 -->
      <el-table-column label="基本单位" align="center" prop="detail.standardUnit" width="100px" />
      <el-table-column label="基本单位实发数量" align="right" prop="detail.standardFulfilledQuantity" width="140px"/>
      <el-table-column label="基本单位应发数量" align="right" prop="detail.standardPlannedQuantity" width="140px"/>

      <!-- 开票信息 -->
      <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" width="100px"/>
      <el-table-column label="开票金额" align="right" prop="detail.invoiceAmount" width="100px"/>

      <!-- 日期信息 -->
      <el-table-column
        label="生产日期"
        align="center"
        prop="detail.effictiveDate"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column
        label="失效日期"
        align="center"
        prop="detail.expiryDate"
        :formatter="dateFormatter"
        width="100px"
      />

      <!-- 其他主单据信息 -->
      <el-table-column label="交易对象订单号" align="left" prop="objectOrderNo" width="140px"/>
      <el-table-column label="摘要" align="left" prop="note" width="120px"/>
      <el-table-column label="备注" align="left" prop="remark" width="120px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审批单号" align="left" prop="approveNo" width="120px"/>
      <el-table-column label="审批人" align="left" prop="approverName" width="100px"/>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveDate"
        :formatter="dateFormatter"
        width="180px"
      />

      <!-- 详细的明细信息 -->
      <el-table-column label="开票基本数量" align="right" prop="detail.standardInvoiceQuantity" width="120px"/>
      <el-table-column label="明细备注" align="left" prop="detail.remark" width="120px"/>
      <el-table-column label="说明" align="left" prop="detail.note" width="120px"/>

      <!-- 系统和标识信息 -->
      <el-table-column label="明细单号" align="left" prop="detail.bizOrderNo" width="120px" />
      <el-table-column label="源单单号" align="left" prop="detail.sourceNo" width="120px"/>
      <el-table-column label="成本对象编码" align="left" prop="detail.costObjectId" width="120px"/>
      <el-table-column label="成本对象名称" align="left" prop="detail.costObjectName" width="120px"/>
      <el-table-column label="记账凭证号" align="left" prop="detail.accountingVoucherNumber" width="120px"/>
      <el-table-column
        label="明细创建时间"
        align="center"
        prop="detail.createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['inventory:delivery-receipt:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['inventory:delivery-receipt:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DeliveryReceiptForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { DeliveryReceiptApi, DeliveryReceiptVO } from '@/api/scm/inventory/deliveryreceipt'
import DeliveryReceiptForm from './DeliveryReceiptForm.vue'
import { DICT_TYPE,getStrDictOptions,getDictLabel } from '@/utils/dict'
/** 销售出库 列表 */
defineOptions({ name: 'DeliveryReceipt' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DeliveryReceiptVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const spanArr = ref<number[]>([]) // 行合并数组
const currentRow = ref() // 当前选中行
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: [],
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  createTime: [],
  approveStatus: undefined,
  approveNo: undefined,
  approverId: undefined,
  approverName: undefined,
  approveDate: [],
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)

/** 扁平化数据 - 将主单据和明细数据合并 */
const flattenedList = computed(() => {
  const result: any[] = []
  spanArr.value = [] // 每次重新计算时清空旧数据

  list.value.forEach(order => {
    const details = order.deliveryReceiptDetails?.length ? order.deliveryReceiptDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })

  return result
})

/** 行合并方法 */
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 需要合并的主信息列
  const mergeFields = ['orderNo', 'bizType', 'sourceType', 'sourceNo', 'objectName', 'date', 'approveStatus', 'objectOrderNo', 'note', 'remark', 'createTime', 'approveNo', 'approverName', 'approveDate']

  if (mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    if (span > 0) {
      return {
        rowspan: span,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}

/** 当前行变化 */
const handleCurrentChange = (currentRow: any) => {
  currentRow.value = currentRow
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeliveryReceiptApi.getDeliveryReceiptPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 为每个主单据获取明细数据
    for (const order of list.value) {
      try {
        const details = await DeliveryReceiptApi.getDeliveryReceiptDetailListByBizOrderId(order.id)
        order.deliveryReceiptDetails = details || []
      } catch (error) {
        console.error(`获取订单 ${order.id} 明细失败:`, error)
        order.deliveryReceiptDetails = []
      }
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeliveryReceiptApi.deleteDeliveryReceipt(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DeliveryReceiptApi.exportDeliveryReceipt(queryParams)
    download.excel(data, '销售出库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
