<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <el-table-column label="序号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.num`" :rules="formRules.num" class="mb-0px!">
            <el-input v-model="row.num" placeholder="请输入序号" />
          </el-form-item>
        </template>
      </el-table-column>
       <el-table-column label="单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.bizOrderNo`" :rules="formRules.bizOrderNo" class="mb-0px!">
            <el-input v-model="row.bizOrderNo" placeholder="请输入单号" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="仓库ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" :rules="formRules.warehouseId" class="mb-0px!">
            <el-input v-model="row.warehouseId" placeholder="请输入仓库ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="库位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.locationId`" :rules="formRules.locationId" class="mb-0px!">
            <el-select v-model="row.locationId" placeholder="请选择库位" :data="locationList" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialId`" :rules="formRules.materialId" class="mb-0px!">
            <el-input v-model="row.materialId" placeholder="请输入物料ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="物料" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialId`" :rules="formRules.materialName" class="mb-0px!">
            <ScrollSelect 
              v-model="row.materialName" 
              :loadMethod="loadMaterials" 
              :labelKey="'name'" 
              :valueKey="'id'" 
              :queryKey="'name'"
              placeholder="请选择物料" 
              :extraParams="{'types':[2,3]}"
              @change="handleMaterialChange($index, $event)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="请输入物料编号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-input v-model="row.unitName" placeholder="请输入单位" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单价" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">
            <el-input v-model="row.unitPrice" placeholder="请输入单价" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="金额" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.amount`" :rules="formRules.amount" class="mb-0px!">
            <el-input v-model="row.amount" placeholder="请输入金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="应收数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.plannedQuantity`" :rules="formRules.plannedQuantity" class="mb-0px!">
            <el-input v-model="row.plannedQuantity" placeholder="请输入应收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="实收数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fulfilledQuantity`" :rules="formRules.fulfilledQuantity" class="mb-0px!">
            <el-input v-model="row.fulfilledQuantity" placeholder="请输入实收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位应收数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardPlannedQuantity`" :rules="formRules.standardPlannedQuantity" class="mb-0px!">
            <el-input v-model="row.standardPlannedQuantity" placeholder="请输入基本单位应收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位实收数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardFulfilledQuantity`" :rules="formRules.standardFulfilledQuantity" class="mb-0px!">
            <el-input v-model="row.standardFulfilledQuantity" placeholder="请输入基本单位实收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardUnit`" :rules="formRules.standardUnit" class="mb-0px!">
            <el-input v-model="row.standardUnit" placeholder="请输入基本单位" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税单价" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxPrice`" :rules="formRules.taxPrice" class="mb-0px!">
            <el-input v-model="row.taxPrice" placeholder="请输入含税单价" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税金额" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxAmount`" :rules="formRules.taxAmount" class="mb-0px!">
            <el-input v-model="row.taxAmount" placeholder="请输入含税金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceQuantity`" :rules="formRules.invoiceQuantity" class="mb-0px!">
            <el-input v-model="row.invoiceQuantity" placeholder="请输入开票数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票金额" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceAmount`" :rules="formRules.invoiceAmount" class="mb-0px!">
            <el-input v-model="row.invoiceAmount" placeholder="请输入开票金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票基本数量" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardInvoiceQuantity`" :rules="formRules.standardInvoiceQuantity" class="mb-0px!">
            <el-input v-model="row.standardInvoiceQuantity" placeholder="请输入开票基本数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="生产日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.effictiveDate`" :rules="formRules.effictiveDate" class="mb-0px!">
            <el-date-picker
              v-model="row.effictiveDate"
              type="date"
              value-format="x"
              placeholder="选择生产日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="失效日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expiryDate`" :rules="formRules.expiryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.expiryDate"
              type="date"
              value-format="x"
              placeholder="选择失效日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="说明" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.note`" :rules="formRules.note" class="mb-0px!">
            <el-input v-model="row.note" placeholder="请输入说明" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="源单ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceId`" :rules="formRules.sourceId" class="mb-0px!">
            <el-input v-model="row.sourceId" placeholder="请输入源单ID" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="源单单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceNo`" :rules="formRules.sourceNo" class="mb-0px!">
            <el-input v-model="row.sourceNo" placeholder="请输入源单单号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="批号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">
            <el-input v-model="row.batchNo" placeholder="请输入批号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="成本对象编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectId`" :rules="formRules.costObjectId" class="mb-0px!">
            <el-input v-model="row.costObjectId" placeholder="请输入成本对象编码" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="成本对象名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectName`" :rules="formRules.costObjectName" class="mb-0px!">
            <el-input v-model="row.costObjectName" placeholder="请输入成本对象名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="记账凭证号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.accountingVoucherNumber`" :rules="formRules.accountingVoucherNumber" class="mb-0px!">
            <el-input v-model="row.accountingVoucherNumber" placeholder="请输入记账凭证号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <!-- <el-button @click="handleDelete($index)" link>—</el-button> -->
           <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加产品入库明细</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { ProductReceiptApi } from '@/api/scm/inventory/productreceipt'
import { WarehouseLocationApi,WarehouseLocationVO } from '@/api/scm/inventory/warehouselocation';
import { MaterialApi } from '@/api/scm/base/material';
import { UnitApi } from '@/api/scm/base/unit';
import { InventoryDetail } from '@/types/inventory';
const props = defineProps({
  bizOrderId: {
    type: [String, Number],
    default: undefined
  },
  warehouseId: {
    type: [String, Number],
    default: undefined
  }
})
const formLoading = ref(false) // 表单的加载中
const formData = ref<InventoryDetail[]>([])
const formRules = reactive<any>({
})
const formRef = ref() // 表单 Ref
const locationList = ref<WarehouseLocationVO[]>([])

//初始化方法
onMounted(async () => {
  await initLocationList()
})
/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.bizOrderId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await ProductReceiptApi.getProductReceiptDetailListByBizOrderId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)
watch(
  () => props.warehouseId,
  async (val) => {
    formData.value = formData.value.map((item:InventoryDetail) => {
      item.warehouseId = val
      return item
    })
  }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row:InventoryDetail = {
    id: undefined,
    num: undefined,
    bizOrderId: undefined,
    bizOrderNo: undefined,
    warehouseId: undefined,
    locationId: undefined,
    materialId: undefined,
    materialName: undefined,
    materialCode: undefined,
    unit: undefined,
    unitPrice: undefined,
    amount: undefined,
    remark: undefined,
    plannedQuantity: undefined,
    fulfilledQuantity: undefined,
    standardPlannedQuantity: undefined,
    standardFulfilledQuantity: undefined,
    standardUnit: undefined,
    taxPrice: undefined,
    taxAmount: undefined,
    invoiceQuantity: undefined,
    invoiceAmount: undefined,
    standardInvoiceQuantity: undefined,
    effictiveDate: undefined,
    expiryDate: undefined,
    note: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    batchNo: undefined,
    costObjectId: undefined,
    costObjectName: undefined,
    accountingVoucherNumber: undefined,
  }
  row.bizOrderId = props.bizOrderId
  row.warehouseId = props.warehouseId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

// 加载物料的方法，用于ScrollSelect组件
const loadMaterials = async (params: any) => {
  try {
    let res = await MaterialApi.getMaterialPage(params)
    return res
  } catch (error) {
    return { list: [], total: 0 }
  }
}
// 处理物料选择变化
const handleMaterialChange = async (index: number, data: any) => {
  if (!data) return
  // 获取选中的物料详情
  const selectedMaterialId = data
  if (selectedMaterialId) {
    const selectedMaterial = await MaterialApi.getMaterial(selectedMaterialId)
    // 更新物料相关信息
    formData.value[index].materialName = selectedMaterial.fullName
    formData.value[index].materialCode = selectedMaterial.fullCode
    if(selectedMaterial.unit){
      formData.value[index].unit = selectedMaterial.unit
      const unit = await UnitApi.getUnit(selectedMaterial.unit)
      formData.value[index].unitName = unit.name
    }
  }
}

//库位信息相关方法
const initLocationList = async () => {
  const res = await WarehouseLocationApi.getWarehouseLocationPage({
    pageNo:1,
    pageSize:10
  })
  locationList.value = res
}
/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>
