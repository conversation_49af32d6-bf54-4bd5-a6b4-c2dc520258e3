<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="订单日期" prop="orderDate">
        <el-date-picker
          v-model="queryParams.orderDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input
          v-model="queryParams.supplierName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="批号" prop="batchNo">
        <el-input
          v-model="queryParams.batchNo"
          placeholder="请输入批号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="需求来源类型" prop="sourceType">
        <el-select
          v-model="queryParams.sourceType"
          placeholder="请选择需求来源类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="来源单号" prop="sourceNo">
        <el-input
          v-model="queryParams.sourceNo"
          placeholder="请输入来源单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['purchase:order-detail:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['purchase:order-detail:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="订单号" align="center" prop="orderNo" />
      <el-table-column
        label="订单日期"
        align="center"
        prop="orderDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="供应商名称" align="center" prop="supplierName" width="100"/>
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="物料数量" align="center" prop="quantity" />
      <el-table-column label="单位" align="center" prop="unit" />
      <el-table-column label="物料单价" align="center" prop="unitPrice" />
      <el-table-column label="含税单价" align="center" prop="unitTaxPrice" />
      <el-table-column label="物料金额" align="center" prop="amount" />
      <el-table-column label="税率" align="center" prop="tax" />
      <el-table-column label="税额" align="center" prop="taxAmount" />
      <el-table-column label="价税合计" align="center" prop="totalAmount" />
      <el-table-column label="已接收数量" align="center" prop="receivedQuantity" width="100"/>
      <el-table-column label="接收日期" align="center" prop="receivedDate" />
      <el-table-column label="批号" align="center" prop="batchNo" />
      <el-table-column label="需求来源类型" align="center" prop="sourceType" width="120"/>
      <el-table-column label="来源单号" align="center" prop="sourceNo" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="商品跟进状态" align="center" prop="progressStatus" width="120"/>
      <el-table-column label="跟进人" align="center" prop="follower" />
      <el-table-column label="跟进日期" align="center" prop="followDate" />
      <el-table-column label="采购进度备注" align="center" prop="progressRemark" width="120"/>
      <el-table-column label="入库数量" align="center" prop="inStockQuantity" />
      <el-table-column label="辅助单位" align="center" prop="auxilaryUnit" />
      <el-table-column label="辅助单位数量" align="center" prop="auxilaryQuantity" width="120"/>
      <el-table-column label="辅助入库数量" align="center" prop="inAuxilaryQuantity" width="120"/>
      <el-table-column label="在途数量" align="center" prop="inTransitQuantity" />
      <el-table-column label="关联数量" align="center" prop="relateQuantity" />
      <el-table-column label="合同单号" align="center" prop="contractNo" />
      <el-table-column label="开票数量" align="center" prop="invoiceQuantity" />
      <el-table-column label="辅助开票数量" align="center" prop="invoiceAuxilaryQuantity" width="120"/>
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['purchase:order-detail:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['purchase:order-detail:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderDetailForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderDetailApi, OrderDetailVO } from '@/api/scm/purchase/orderdetail'
import OrderDetailForm from './OrderDetailForm.vue'

/** 采购订单产品 列表 */
defineOptions({ name: 'OrderDetail' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OrderDetailVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  orderDate: [],
  supplierName: undefined,
  materialName: undefined,
  batchNo: undefined,
  sourceType: undefined,
  sourceNo: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderDetailApi.getOrderDetailPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderDetailApi.deleteOrderDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderDetailApi.exportOrderDetail(queryParams)
    download.excel(data, '采购订单产品.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
