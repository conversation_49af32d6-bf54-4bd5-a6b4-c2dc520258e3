<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <!-- 始终显示的搜索项 -->
      <el-form-item label="单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="bizType">
        <el-select
          v-model="queryParams.bizType"
          placeholder="请选择业务类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="approveStatus">
        <el-select
          v-model="queryParams.approveStatus"
          placeholder="请选择审批状态"
          clearable
          class="!w-240px"
        >
          <el-option
          v-for="item in approve_status"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-show="!isExpanded">
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="text"
          @click="toggleExpanded"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>

      <!-- 展开的搜索项 -->
      <template v-if="isExpanded">
        <el-form-item label="来源类型" prop="sourceType">
          <el-select
            v-model="queryParams.sourceType"
            placeholder="请选择来源类型"
            clearable
            class="!w-240px"
          >
            <el-option
            v-for="item in material_source"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源单编号" prop="sourceNo">
          <el-input
            v-model="queryParams.sourceNo"
            placeholder="请输入来源单编号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交易对象名称" prop="objectName">
          <el-input
            v-model="queryParams.objectName"
            placeholder="请输入交易对象名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交易对象订单号" prop="objectOrderNo">
          <el-input
            v-model="queryParams.objectOrderNo"
            placeholder="请输入交易对象订单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交易日期" prop="date">
          <el-date-picker
            v-model="queryParams.date"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="摘要" prop="note">
          <el-input
            v-model="queryParams.note"
            placeholder="请输入摘要"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="queryParams.remark"
            placeholder="请输入备注"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批单号" prop="approveNo">
          <el-input
            v-model="queryParams.approveNo"
            placeholder="请输入审批单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批人" prop="approverName">
          <el-input
            v-model="queryParams.approverName"
            placeholder="请输入审批人"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批时间" prop="approveDate">
          <el-date-picker
            v-model="queryParams.approveDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            plain
            @click="openForm('create')"
            v-hasPermi="['inventory:purchase-receipt:create']"
          >
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button
            type="success"
            plain
            @click="handleExport"
            :loading="exportLoading"
            v-hasPermi="['inventory:purchase-receipt:export']"
          >
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
          <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
          <el-button
            type="text"
            @click="toggleExpanded"
            class="ml-2"
          >
            {{ isExpanded ? '收起' : '展开' }}
            <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
          </el-button>
        </el-form-item>
      </template>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
      :span-method="objectSpanMethod"
      height="600"
      style="width: 100%"
    >
      <el-table-column label="单号" align="left" prop="orderNo" width="120px" fixed="left"/>
      <el-table-column label="业务类型" align="left" prop="bizType" width="100px" fixed="left"/>

      <!-- 明细信息列 - 按重要性排序 -->
      <el-table-column label="序号" align="center" prop="detail.num" width="60px" />
      <el-table-column label="物料名称" align="left" prop="detail.materialName" width="180px" fixed="left"/>
      <el-table-column label="物料编号" align="left" prop="detail.materialCode" width="120px" />
      <el-table-column label="应收数量" align="right" prop="detail.plannedQuantity" width="100px"/>
      <el-table-column label="实收数量" align="right" prop="detail.fulfilledQuantity" width="100px"/>
      <el-table-column label="单位" align="center" prop="detail.unit" width="80px" />
      <el-table-column label="单价" align="right" prop="detail.unitPrice" width="100px"/>
      <el-table-column label="金额" align="right" prop="detail.amount" width="100px"/>
      <el-table-column label="含税单价" align="right" prop="detail.taxPrice" width="100px"/>
      <el-table-column label="含税金额" align="right" prop="detail.taxAmount" width="110px"/>
      <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>
      <el-table-column label="基本单位" align="center" prop="detail.standardUnit" width="100px" />
      <el-table-column label="基本单位应收数量" align="right" prop="detail.standardPlannedQuantity" width="140px"/>
      <el-table-column label="基本单位实收数量" align="right" prop="detail.standardFulfilledQuantity" width="140px"/>
      <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" width="100px"/>
      <el-table-column label="开票金额" align="right" prop="detail.invoiceAmount" width="100px"/>
      <el-table-column label="开票基本数量" align="right" prop="detail.standardInvoiceQuantity" width="120px"/>
      <el-table-column
        label="生产日期"
        align="center"
        prop="detail.effictiveDate"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column
        label="失效日期"
        align="center"
        prop="detail.expiryDate"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column label="明细单号" align="left" prop="detail.bizOrderNo" width="120px" />
      <el-table-column label="源单单号" align="left" prop="detail.sourceNo" width="120px"/>
      <el-table-column label="成本对象名称" align="left" prop="detail.costObjectName" width="120px"/>
      <el-table-column label="记账凭证号" align="left" prop="detail.accountingVoucherNumber" width="120px"/>
      <el-table-column label="说明" align="left" prop="detail.note" width="120px"/>
      <el-table-column label="明细备注" align="left" prop="detail.remark" width="120px"/>
      <el-table-column
        label="明细创建时间"
        align="center"
        prop="detail.createTime"
        :formatter="dateFormatter"
        width="180px"
      />

      <el-table-column label="来源类型" align="center" prop="sourceType" width="100px">
        <template #default="scope">
          {{ getDictLabel('MATERIAL_SOURCE', scope.row.sourceType) }}
        </template>
      </el-table-column>
      <el-table-column label="来源单编号" align="left" prop="sourceNo" width="120px"/>
      <el-table-column label="交易对象名称" align="left" prop="objectName" width="150px"/>
      <el-table-column label="交易对象订单号" align="left" prop="objectOrderNo" width="140px"/>
      <el-table-column
        label="交易日期"
        align="center"
        prop="date"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column label="摘要" align="left" prop="note" width="120px"/>
      <el-table-column label="备注" align="left" prop="remark" width="120px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审批状态" align="center" prop="approveStatus" width="100px">
        <template #default="scope">
          {{ getDictLabel('APPROVE_STATUS', scope.row.approveStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="审批单号" align="left" prop="approveNo" width="120px"/>
      <el-table-column label="审批人" align="left" prop="approverName" width="100px"/>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right" prop="operation">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['inventory:purchase-receipt:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['inventory:purchase-receipt:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PurchaseReceiptForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { PurchaseReceiptApi, PurchaseReceiptVO } from '@/api/scm/inventory/purchasereceipt'
import PurchaseReceiptForm from './PurchaseReceiptForm.vue'
import { DICT_TYPE,getStrDictOptions,getDictLabel } from '@/utils/dict'
/** 采购入库 列表 */
defineOptions({ name: 'PurchaseReceipt' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<PurchaseReceiptVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const isExpanded = ref(false) // 搜索表单展开状态
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: [],
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  createTime: [],
  approveStatus: undefined,
  approveNo: undefined,
  approverId: undefined,
  approverName: undefined,
  approveDate: [],
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PurchaseReceiptApi.getPurchaseReceiptPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 为每个主单据获取明细数据
    for (const order of list.value) {
      try {
        const details = await PurchaseReceiptApi.getPurchaseReceiptDetailListByBizOrderId(order.id)
        order.purchaseReceiptDetails = details || []
      } catch (error) {
        console.error(`获取订单 ${order.id} 明细失败:`, error)
        order.purchaseReceiptDetails = []
      }
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换展开/收起状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PurchaseReceiptApi.deletePurchaseReceipt(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PurchaseReceiptApi.exportPurchaseReceipt(queryParams)
    download.excel(data, '采购入库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref({}) // 选中行
const handleCurrentChange = (row: any) => {
  currentRow.value = row
}

// 定义合并行数存储数组
const spanArr = ref<number[]>([])

const flattenedList = computed(() => {
  const result: any[] = []
  spanArr.value = [] // 每次重新计算时清空旧数据

  list.value.forEach(order => {
    const details = order.purchaseReceiptDetails?.length ? order.purchaseReceiptDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })

  return result
})

// 需要合并的主信息列（必须与el-table-column的prop严格匹配）
const mergeFields = [
  'orderNo', 'bizType', 'sourceType', 'sourceNo', 'objectName', 'objectOrderNo',
  'date', 'note', 'remark', 'createTime', 'approveStatus', 'approveNo',
  'approverName', 'approveDate', 'operation'
]

const objectSpanMethod = ({ column, rowIndex }: { row: any, column: any, rowIndex: number }) => {
  // 处理主信息列合并
  if (mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    return {
      rowspan: span, // 合并行数
      colspan: span > 0 ? 1 : 0 // 0表示隐藏单元格
    }
  }
  // 明细列不合并
  return { rowspan: 1, colspan: 1 }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
