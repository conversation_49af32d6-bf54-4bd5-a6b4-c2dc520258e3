<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-row :gutter="20">
        <!-- 第一行：始终显示的搜索项 -->
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="订单单号" prop="orderNo">
            <el-input
              v-model="queryParams.orderNo"
              placeholder="请输入订单单号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              v-model="queryParams.customerName"
              placeholder="请输入客户名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="审批状态" prop="approveStatus">
            <el-select
              v-model="queryParams.approveStatus"
              placeholder="请选择审批状态"
              clearable
              class="!w-240px"
            >
              <el-option label="请选择字典生成" value="" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6" v-show="!isExpanded">
          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button
              type="text"
              @click="toggleExpanded"
              class="ml-2"
            >
              {{ isExpanded ? '收起' : '展开' }}
              <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 展开的搜索项 -->
      <el-row v-show="isExpanded" :gutter="20">
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="联系人" prop="customerContact">
            <el-input
              v-model="queryParams.customerContact"
              placeholder="请输入联系人姓名"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="联系电话" prop="customerPhone">
            <el-input
              v-model="queryParams.customerPhone"
              placeholder="请输入联系电话"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="收货地址" prop="customerAddress">
            <el-input
              v-model="queryParams.customerAddress"
              placeholder="请输入收货地址"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="发货日期" prop="deliveryDate">
            <el-date-picker
              v-model="queryParams.deliveryDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="送达日期" prop="expectedArrivalDate">
            <el-date-picker
              v-model="queryParams.expectedArrivalDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="运输方式" prop="transportMethod">
            <el-input
              v-model="queryParams.transportMethod"
              placeholder="请输入运输方式"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="物流公司" prop="logisticsCompany">
            <el-input
              v-model="queryParams.logisticsCompany"
              placeholder="请输入物流公司"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="6">
          <el-form-item label="物流单号" prop="logisticsTrackingNumber">
            <el-input
              v-model="queryParams.logisticsTrackingNumber"
              placeholder="请输入物流单号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 操作按钮行 -->
      <el-row v-show="isExpanded" :gutter="20">
        <el-col :span="24">
          <el-form-item>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['sale:delivery-notice:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['sale:delivery-notice:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
            <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button
              type="text"
              @click="toggleExpanded"
              class="ml-2"
            >
              {{ isExpanded ? '收起' : '展开' }}
              <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
      :span-method="objectSpanMethod"
    >
      <el-table-column label="订单单号" align="left" prop="orderNo" width="100px"/>
      <el-table-column label="客户名称" align="left" prop="customerName" width="100px"/>
      <el-table-column label="联系人" align="left" prop="customerContact" />
      <el-table-column label="联系电话" align="left" prop="customerPhone" width="100px"/>
      <el-table-column label="收货地址" align="left" prop="customerAddress" width="100px"/>

      <el-table-column label="产品名称" align="left" prop="detail.materialName" width="180px" />
      <el-table-column label="编号" align="left" prop="detail.materialCode" width="120px" />
      <el-table-column label="规格" align="left" prop="detail.materialSpec" width="120px" />
      <el-table-column label="数量" align="left" prop="detail.materialQuantity" width="100px"/>
      <el-table-column label="单位" align="left" prop="detail.materialUnit" width="120px" />
      <el-table-column label="总价" align="left" prop="detail.totalAmount" width="110px"/>
      <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>
      <el-table-column label="件数" align="left" prop="detail.packQuantity" width="100px"/>

      <el-table-column label="发货日期" align="center" prop="deliveryDate" width="100px"/>
      <el-table-column label="送达日期" align="center" prop="expectedArrivalDate" width="100px"/>
      <el-table-column label="运输方式" align="center" prop="transportMethod" width="100px"/>
      <el-table-column label="物流公司" align="left" prop="logisticsCompany" width="100px"/>
      <el-table-column label="物流单号" align="left" prop="logisticsTrackingNumber" width="100px"/>
      <el-table-column label="说明事项" align="left" prop="remarks" width="100px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审批单号" align="center" prop="approveNo" width="100px"/>
      <el-table-column label="审批状态" align="center" prop="approveStatus" width="100px"/>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审批人" align="center" prop="approverName" />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right" prop="operation">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['sale:delivery-notice:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['sale:delivery-notice:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DeliveryNoticeForm ref="formRef" @success="getList" />
  <!-- 子表的列表 -->
  <!-- <ContentWrap>
    <el-tabs model-value="deliveryNoticeDetail">
      <el-tab-pane label="发货通知单明细" name="deliveryNoticeDetail">
        <DeliveryNoticeDetailList :notice-id="currentRow.id" />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap> -->
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { DeliveryNoticeApi, DeliveryNoticeVO } from '@/api/scm/sale/deliverynotice'
import DeliveryNoticeForm from './DeliveryNoticeForm.vue'
// import DeliveryNoticeDetailList from './components/DeliveryNoticeDetailList.vue'

/** 发货通知单 列表 */
defineOptions({ name: 'DeliveryNotice' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DeliveryNoticeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const isExpanded = ref(false) // 搜索表单展开状态
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderId: undefined,
  orderNo: undefined,
  customerName: undefined,
  customerContact: undefined,
  customerPhone: undefined,
  customerAddress: undefined,
  deliveryDate: [],
  expectedArrivalDate: [],
  transportMethod: undefined,
  logisticsCompany: undefined,
  logisticsTrackingNumber: undefined,
  remarks: undefined,
  createTime: [],
  kdId: undefined,
  customerId: undefined,
  approveNo: undefined,
  approveStatus: undefined,
  approveDate: [],
  approverId: undefined,
  approverName: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeliveryNoticeApi.getDeliveryNoticePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换展开/收起状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeliveryNoticeApi.deleteDeliveryNotice(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DeliveryNoticeApi.exportDeliveryNotice(queryParams)
    download.excel(data, '发货通知单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref({}) // 选中行
const handleCurrentChange = (row) => {
  currentRow.value = row
}

// 新增：定义合并行数存储数组
const spanArr = ref<number[]>([])

const flattenedList = computed(() => {
  const result = []
  spanArr.value = [] // 每次重新计算时清空旧数据
  list.value.forEach(order => {
    const details = order.details?.length ? order.details : [{}] // 确保无明细时也有占位行
    const detailCount = details.length
    details.forEach((detail, index) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })
  return result
})

// 需要合并的主信息列（必须与el-table-column的prop严格匹配）
const mergeFields = [
  'id','orderNo', 'customerName', 'customerContact', 'customerPhone', 'customerAddress',
  'deliveryDate', 'expectedArrivalDate', 'transportMethod', 'logisticsCompany',
  'logisticsTrackingNumber', 'remarks', 'approveNo', 'approveStatus', 'approveDate',
  'createTime','operation'
]

const objectSpanMethod = ({ row, column, rowIndex }) => {
  // 处理主信息列合并
  if (mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    return {
      rowspan: span, // 合并行数
      colspan: span > 0 ? 1 : 0 // 0表示隐藏单元格
    }
  }
  // 商品明细列不合并
  return { rowspan: 1, colspan: 1 }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
