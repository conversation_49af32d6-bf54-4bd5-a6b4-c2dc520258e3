<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%" >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
      inline
    >
      <!-- <el-form-item label="订单ID" prop="orderId">
        <el-input v-model="formData.orderId" placeholder="请输入订单ID" />
      </el-form-item> -->
      <el-form-item label="订单单号" prop="orderNo">
        <el-input v-model="formData.orderNo" placeholder="请输入订单单号" />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input v-model="formData.customerName" placeholder="请输入客户名称" />
      </el-form-item>
      <el-form-item label="联系人姓名" prop="customerContact">
        <el-input v-model="formData.customerContact" placeholder="请输入联系人姓名" />
      </el-form-item>
      <el-form-item label="联系电话" prop="customerPhone">
        <el-input v-model="formData.customerPhone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="收货地址" prop="customerAddress">
        <el-input v-model="formData.customerAddress" placeholder="请输入收货地址" />
      </el-form-item>
      <el-form-item label="发货日期" prop="deliveryDate">
        <el-date-picker
          v-model="formData.deliveryDate"
          type="date"
          value-format="x"
          placeholder="选择发货日期"
        />
      </el-form-item>
      <el-form-item label="送达日期" prop="expectedArrivalDate">
        <el-date-picker
          v-model="formData.expectedArrivalDate"
          type="date"
          value-format="x"
          placeholder="选择送达日期"
        />
      </el-form-item>
      <el-form-item label="运输方式" prop="transportMethod">
        <el-input v-model="formData.transportMethod" placeholder="请输入运输方式" />
      </el-form-item>
      <el-form-item label="物流公司" prop="logisticsCompany">
        <el-input v-model="formData.logisticsCompany" placeholder="请输入物流公司" />
      </el-form-item>
      <el-form-item label="物流单号" prop="logisticsTrackingNumber">
        <el-input v-model="formData.logisticsTrackingNumber" placeholder="请输入物流单号" />
      </el-form-item>
      <el-form-item label="说明事项" prop="remarks">
        <el-input v-model="formData.remarks" placeholder="请输入说明事项" />
      </el-form-item>
      <el-form-item label="租户名称" prop="tenantName">
        <el-input v-model="formData.tenantName" placeholder="请输入租户名称" />
      </el-form-item>
      <el-form-item label="审批单号" prop="approveNo">
        <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
      </el-form-item>
      <el-form-item label="审批状态" prop="approveStatus">
        <el-radio-group v-model="formData.approveStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审批时间" prop="approveDate">
        <el-date-picker
          v-model="formData.approveDate"
          type="date"
          value-format="x"
          placeholder="选择审批时间"
        />
      </el-form-item>
      <el-form-item label="审批人名称" prop="approverName">
        <el-input v-model="formData.approverName" placeholder="请输入审批人名称" />
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="发货通知单明细" name="deliveryNoticeDetail">
        <DeliveryNoticeDetailForm ref="deliveryNoticeDetailFormRef" :notice-id="formData.id" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DeliveryNoticeApi, DeliveryNoticeVO } from '@/api/scm/sale/deliverynotice'
import DeliveryNoticeDetailForm from './components/DeliveryNoticeDetailForm.vue'

/** 发货通知单 表单 */
defineOptions({ name: 'DeliveryNoticeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  orderId: undefined,
  orderNo: undefined,
  customerName: undefined,
  customerContact: undefined,
  customerPhone: undefined,
  customerAddress: undefined,
  deliveryDate: undefined,
  expectedArrivalDate: undefined,
  transportMethod: undefined,
  logisticsCompany: undefined,
  logisticsTrackingNumber: undefined,
  remarks: undefined,
  kdId: undefined,
  customerId: undefined,
  tenantName: undefined,
  approveNo: undefined,
  approveStatus: undefined,
  approveDate: undefined,
  approverId: undefined,
  approverName: undefined,
})
const formRules = reactive({
  orderId: [{ required: true, message: '订单ID不能为空', trigger: 'blur' }],
  customerName: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
  customerContact: [{ required: true, message: '联系人姓名不能为空', trigger: 'blur' }],
  customerPhone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
  customerAddress: [{ required: true, message: '收货地址不能为空', trigger: 'blur' }],
  deliveryDate: [{ required: true, message: '发货日期不能为空', trigger: 'blur' }],
  transportMethod: [{ required: true, message: '运输方式不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('deliveryNoticeDetail')
const deliveryNoticeDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DeliveryNoticeApi.getDeliveryNotice(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await deliveryNoticeDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'deliveryNoticeDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DeliveryNoticeVO
    // 拼接子表的数据
    data.deliveryNoticeDetails = deliveryNoticeDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await DeliveryNoticeApi.createDeliveryNotice(data)
      message.success(t('common.createSuccess'))
    } else {
      await DeliveryNoticeApi.updateDeliveryNotice(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    orderId: undefined,
    orderNo: undefined,
    customerName: undefined,
    customerContact: undefined,
    customerPhone: undefined,
    customerAddress: undefined,
    deliveryDate: undefined,
    expectedArrivalDate: undefined,
    transportMethod: undefined,
    logisticsCompany: undefined,
    logisticsTrackingNumber: undefined,
    remarks: undefined,
    kdId: undefined,
    customerId: undefined,
    tenantName: undefined,
    approveNo: undefined,
    approveStatus: undefined,
    approveDate: undefined,
    approverId: undefined,
    approverName: undefined,
  }
  formRef.value?.resetFields()
}
</script>
