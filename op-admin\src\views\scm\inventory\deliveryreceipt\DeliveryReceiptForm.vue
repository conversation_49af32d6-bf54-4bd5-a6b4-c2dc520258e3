<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单号" prop="orderNo">
            <el-input v-model="formData.orderNo" placeholder="请输入单号(自动生成)" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务类型" prop="bizType">
            <el-select v-model="formData.bizType" placeholder="请选择业务类型">
              <el-option label="请选择字典生成" value="" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源类型" prop="sourceType">
            <el-select v-model="formData.sourceType" placeholder="请选择来源类型">
              <el-option 
              v-for="item in material_source" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="来源单ID" prop="sourceId">
            <el-input v-model="formData.sourceId" placeholder="请输入来源单ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="来源单" prop="sourceNo">
            <ScrollSelect
              v-model="formData.sourceNo"
              :load-method="loadSalesOrders"
              label-key="orderNoWithCustomer"
              value-key="orderNo"
              query-key="orderNo"
              :default-value="salesOrderDefaultValue"
              :extra-params="salesOrderExtraParams"
              @change="onSalesOrderChange"
              placeholder="请选择来源单"
              :key="formData.sourceNo"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="交易对象ID" prop="objectId">
            <el-input v-model="formData.objectId" placeholder="请输入交易对象ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="交易对象名称" prop="objectName">
            <el-input v-model="formData.objectName" placeholder="请输入交易对象名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="交易对象订单号" prop="objectOrderNo">
            <el-input v-model="formData.objectOrderNo" placeholder="请输入交易对象订单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="交易日期" prop="date">
            <el-date-picker
              v-model="formData.date"
              type="date"
              value-format="x"
              placeholder="选择交易日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库名称" prop="warehouseId">
            <el-tree-select v-model="formData.warehouseId" :data="warehouseList" placeholder="请选择仓库"/>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="科目ID" prop="accountId">
            <el-input v-model="formData.accountId" placeholder="请输入科目ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="摘要" prop="note">
            <el-input v-model="formData.note" placeholder="请输入摘要" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="审批状态" prop="approveStatus">
            <el-radio-group v-model="formData.approveStatus">
              <el-radio 
              v-for="item in approve_status" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批单号" prop="approveNo">
            <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人ID" prop="approverId">
            <el-input v-model="formData.approverId" placeholder="请输入审批人ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人" prop="approverName">
            <el-input v-model="formData.approverName" placeholder="请输入审批人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批时间" prop="approveDate">
            <el-date-picker
              v-model="formData.approveDate"
              type="date"
              value-format="x"
              placeholder="选择审批时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门ID" prop="deptId">
            <el-input v-model="formData.deptId" placeholder="请输入部门ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务员ID" prop="empId">
            <el-input v-model="formData.empId" placeholder="请输入业务员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员ID" prop="managerId">
            <el-input v-model="formData.managerId" placeholder="请输入管理员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员1ID" prop="manger1Id">
            <el-input v-model="formData.manger1Id" placeholder="请输入管理员1ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="记账ID" prop="accountantId">
            <el-input v-model="formData.accountantId" placeholder="请输入记账ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检验员ID" prop="checkerId">
            <el-input v-model="formData.checkerId" placeholder="请输入检验员ID" />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="销售出库明细" name="deliveryReceiptDetail">
        <DeliveryReceiptDetailForm ref="deliveryReceiptDetailFormRef" :biz-order-id="formData.id" :warehouse-id="formData.warehouseId" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DeliveryReceiptApi, DeliveryReceiptVO } from '@/api/scm/inventory/deliveryreceipt'
import DeliveryReceiptDetailForm from './components/DeliveryReceiptDetailForm.vue'
import { DICT_TYPE,getStrDictOptions } from '@/utils/dict'
import { WarehouseApi } from '@/api/scm/inventory/warehouse'
import { handleTree } from '@/utils/tree'
import { OrderApi, OrderVO } from '@/api/scm/sale/order'
import ScrollSelect from '@/components/ScrollSelect/index.vue'
/** 销售出库 表单 */
defineOptions({ name: 'DeliveryReceiptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const warehouseList = ref<any[]>([])

// 销售订单选择器相关
const salesOrderDefaultValue = ref({})
const salesOrderExtraParams = ref({})
const formData = ref({
  id: undefined as number | undefined,
  orderNo: undefined as string | undefined,
  bizType: undefined as string | undefined,
  sourceType: undefined as string | undefined,
  sourceId: undefined as number | undefined,
  sourceNo: undefined as string | undefined,
  objectId: undefined as number | undefined,
  objectName: undefined as string | undefined,
  objectOrderNo: undefined as string | undefined,
  date: undefined as Date | undefined,
  warehouseId: undefined as number | undefined,
  accountId: undefined as number | undefined,
  note: undefined as string | undefined,
  remark: undefined as string | undefined,
  approveStatus: undefined as number | undefined,
  approveNo: undefined as string | undefined,
  approverId: undefined as number | undefined,
  approverName: undefined as string | undefined,
  approveDate: undefined as Date | undefined,
  deptId: undefined as number | undefined,
  empId: undefined as number | undefined,
  managerId: undefined as number | undefined,
  manger1Id: undefined as number | undefined,
  accountantId: undefined as number | undefined,
  checkerId: undefined as number | undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('deliveryReceiptDetail')
const deliveryReceiptDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  await initWarehouseList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DeliveryReceiptApi.getDeliveryReceipt(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await deliveryReceiptDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'deliveryReceiptDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DeliveryReceiptVO
    // 拼接子表的数据
    data.deliveryReceiptDetails = deliveryReceiptDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await DeliveryReceiptApi.createDeliveryReceipt(data)
      message.success(t('common.createSuccess'))
    } else {
      await DeliveryReceiptApi.updateDeliveryReceipt(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 加载销售订单数据 */
const loadSalesOrders = async (params: any) => {
  try {
    const response = await OrderApi.getOrderPage({
      pageNo: params.page || 1,
      pageSize: params.size || 20,
      orderNo: params.query || '',
      ...salesOrderExtraParams.value
    })

    const orders = response.list || []

    // 为每个订单添加显示标签（订单号 + 客户名称）
    const processedOrders = orders.map((order: OrderVO) => ({
      ...order,
      orderNoWithCustomer: `${order.orderNo} - ${order.customerName || '未知客户'}`
    }))

    return {
      list: processedOrders,
      total: response.total || 0
    }
  } catch (error) {
    console.error('加载销售订单失败:', error)
    return {
      list: [],
      total: 0
    }
  }
}

/** 销售订单选择变化 */
const onSalesOrderChange = async (selectedOrder: OrderVO | null) => {
  if (selectedOrder) {
    // 设置来源单相关信息
    formData.value.sourceId = selectedOrder.orderId
    formData.value.sourceNo = selectedOrder.orderNo
    formData.value.objectId = selectedOrder.customerId
    formData.value.objectName = selectedOrder.customerName
    formData.value.objectOrderNo = selectedOrder.orderNo

    // 加载销售订单明细并添加到出库明细中
    await loadSalesOrderDetails(selectedOrder.orderId)
  } else {
    // 清空相关字段
    formData.value.sourceId = undefined
    formData.value.sourceNo = undefined
    formData.value.objectId = undefined
    formData.value.objectName = undefined
    formData.value.objectOrderNo = undefined
  }
}

/** 加载销售订单明细并添加到销售出库明细中 */
const loadSalesOrderDetails = async (orderId: number) => {
  try {
    console.log('开始加载销售订单明细，订单ID:', orderId, '类型:', typeof orderId)

    // 确保orderId是数字类型
    const orderIdNumber = Number(orderId)
    if (isNaN(orderIdNumber)) {
      console.error('订单ID不是有效的数字:', orderId)
      return
    }

    console.log('调用API，订单ID:', orderIdNumber)

    // 获取销售订单详情（包含明细）
    const orderDetails = await OrderApi.getOrder(orderIdNumber, true)
    console.log('API返回的原始数据:', orderDetails)

    // 检查返回的数据结构
    const detailList = orderDetails?.orderDetails || []
    console.log('处理后的明细列表:', detailList, '长度:', detailList.length)

    if (detailList && detailList.length > 0) {
      console.log('第一个明细项:', detailList[0])

      // 将销售订单明细转换为销售出库明细格式
      const deliveryDetails = detailList.map((detail: any, index: number) => ({
        id: undefined, // 新增时ID为空
        num: index + 1, // 序号
        bizOrderId: formData.value.id, // 销售出库单ID
        bizOrderNo: formData.value.orderNo, // 销售出库单号
        warehouseId: formData.value.warehouseId, // 仓库ID
        locationId: undefined, // 库位ID
        materialId: detail.productId, // 物料ID（使用产品ID）
        materialName: detail.productName, // 物料名称（使用产品名称）
        materialCode: detail.productCode, // 物料编号（使用产品编号）
        unit: detail.unit, // 单位
        unitPrice: detail.unitPrice || 0, // 单价
        amount: detail.amount || 0, // 金额
        remark: detail.remark || '', // 明细备注
        plannedQuantity: detail.quantity || 0, // 应发数量（来自销售订单的数量）
        fulfilledQuantity: detail.quantity || 0, // 实发数量（默认等于应发数量，用户可修改）
        standardPlannedQuantity: detail.standardQuantity || 0, // 基本单位应发数量
        standardFulfilledQuantity: detail.standardQuantity || 0, // 基本单位实发数量
        standardUnit: detail.standardUnit || '', // 基本单位
        taxPrice: detail.taxPrice || 0, // 含税单价
        taxAmount: detail.taxAmount || 0, // 含税金额
        invoiceQuantity: 0, // 开票数量
        invoiceAmount: 0, // 开票金额
        standardInvoiceQuantity: 0, // 开票基本数量
        effictiveDate: undefined, // 生产日期
        expiryDate: undefined, // 失效日期
        note: detail.note || '', // 说明
        sourceId: detail.id, // 源单ID（销售订单明细ID）
        sourceNo: formData.value.sourceNo, // 源单单号（使用主单的sourceNo）
        batchNo: '', // 批号
        costObjectId: undefined, // 成本对象编码
        costObjectName: undefined, // 成本对象名称
        accountingVoucherNumber: undefined, // 记账凭证号
      }))

      console.log('转换后的销售出库明细:', deliveryDetails)

      // 将明细数据设置到子表单中
      if (deliveryReceiptDetailFormRef.value && deliveryReceiptDetailFormRef.value.setData) {
        deliveryReceiptDetailFormRef.value.setData(deliveryDetails)
        console.log('明细数据已设置到子表单')
      } else {
        console.error('deliveryReceiptDetailFormRef 或 setData 方法未找到')
      }
    } else {
      console.log('销售订单明细为空或未找到')
    }
  } catch (error: any) {
    console.error('加载销售订单明细失败:', error)
    console.error('错误详情:', error?.message || error)
    console.error('错误堆栈:', error?.stack || '无堆栈信息')
    console.error('错误响应:', error?.response || '无响应信息')
  }
}

//初始化仓库数据
const initWarehouseList = async () => {
  const res = await WarehouseApi.getWarehouseList({
    pageNo:1,
    pageSize:100
  })
  const formatTreeData = (list: any[]) => {
    return list.map(item => {
      const node:any = {
        id: item.id,
        label: item.name,
        value: item.id,
        parentId: item.parentId
      }

      if (item.children && item.children.length > 0) {
        node.children = formatTreeData(item.children)
      }

      return node
    })
  }
  const warehouseTree = handleTree(res, 'id', 'parentId')
  warehouseList.value = formatTreeData(warehouseTree)
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined as number | undefined,
    orderNo: undefined as string | undefined,
    bizType: undefined as string | undefined,
    sourceType: undefined as string | undefined,
    sourceId: undefined as number | undefined,
    sourceNo: undefined as string | undefined,
    objectId: undefined as number | undefined,
    objectName: undefined as string | undefined,
    objectOrderNo: undefined as string | undefined,
    date: undefined as Date | undefined,
    warehouseId: undefined as number | undefined,
    accountId: undefined as number | undefined,
    note: undefined as string | undefined,
    remark: undefined as string | undefined,
    approveStatus: undefined as number | undefined,
    approveNo: undefined as string | undefined,
    approverId: undefined as number | undefined,
    approverName: undefined as string | undefined,
    approveDate: undefined as Date | undefined,
    deptId: undefined as number | undefined,
    empId: undefined as number | undefined,
    managerId: undefined as number | undefined,
    manger1Id: undefined as number | undefined,
    accountantId: undefined as number | undefined,
    checkerId: undefined as number | undefined,
  }
  formRef.value?.resetFields()
}
</script>
